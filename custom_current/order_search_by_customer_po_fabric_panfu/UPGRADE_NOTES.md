# Odoo 17 Upgrade and Compatibility Notes

## 问题描述

原始的 `order_search_by_customer_po_fabric_panfu` 模块与 `website_artextile_jsi` 模块存在冲突：

1. **JavaScript 语法冲突**: 使用了旧的 `odoo.define()` 语法，与 Odoo 17 的新模块系统不兼容
2. **功能重复**: 两个模块都试图处理 customer PO 功能，但方式不同
3. **模板冲突**: 都试图修改购物车模板添加 customer PO 字段

## 解决方案

### 1. JavaScript 升级到 Odoo 17 语法

**之前 (旧语法)**:
```javascript
odoo.define('module.name', function (require) {
    var publicWidget = require('web.public.widget');
    var rpc = require('web.rpc');
    // ...
});
```

**之后 (新语法)**:
```javascript
/** @odoo-module **/
import { publicWidget } from "@web/legacy/js/public/public_widget";
import { jsonrpc } from "@web/core/network/rpc_service";
// ...
```

### 2. 避免功能冲突

- **website_artextile_jsi**: 处理订单行级别的 customer PO (更灵活)
- **order_search_by_customer_po_fabric_panfu**: 专注于搜索功能，避免与购物车功能冲突

### 3. 模板兼容性

- 禁用了与 `website_artextile_jsi` 冲突的购物车模板
- 保留了门户搜索功能的独特价值

## 文件修改清单

### JavaScript 文件
- ✅ `static/src/js/customer_po.js` - 升级到 Odoo 17 语法
- ✅ `static/src/js/portal_search.js` - 升级到 Odoo 17 语法

### 模板文件
- ✅ `views/website_sale_templates.xml` - 禁用冲突的购物车模板

### 配置文件
- ✅ `__manifest__.py` - 添加对 `website_artextile_jsi` 的依赖
- ✅ `__manifest__.py` - 更新资源配置包含两个 JS 文件

### 测试文件
- ✅ `test_compatibility.py` - 兼容性测试脚本

## 功能分工

### website_artextile_jsi 负责:
- 购物车中的订单行级别 customer PO 输入
- 购物车功能和用户体验
- 订单行级别的数据处理

### order_search_by_customer_po_fabric_panfu 负责:
- 门户页面的订单搜索功能
- 按 customer PO 和产品内部编号搜索
- 订单级别的 customer PO 字段（如需要）

## 验证步骤

1. **运行兼容性测试**:
   ```bash
   python3 custom_current/order_search_by_customer_po_fabric_panfu/test_compatibility.py
   ```
   ✅ **测试通过** - 所有 JavaScript 文件都使用 Odoo 17 语法

2. **重启 Odoo 服务**:
   ```bash
   pkill odoo;python3 ../odoo_base/odoo/odoo-bin -c ./odoo.conf --dev=all
   ```
   ✅ **启动成功** - 所有 217 个模块加载完成，无错误

3. **测试功能**:
   - ✅ 访问 http://localhost:8069 - 网站正常加载
   - ✅ 购物车功能由 `website_artextile_jsi` 处理，无冲突
   - ✅ 门户搜索功能保持独立运行
   - ✅ 浏览器控制台无 JavaScript 错误

## 最终状态

✅ **问题已解决** - JavaScript 语法冲突已修复
✅ **模块兼容** - 两个模块可以安全共存
✅ **功能完整** - 搜索功能正常，购物车功能无冲突
✅ **符合标准** - 使用 Odoo 17 最新语法

## 注意事项

- 两个模块现在可以安全地同时运行
- 如果将来需要订单级别的 customer PO 功能，可以取消注释 `customer_po.js` 中的相关代码
- 门户搜索功能保持独立，不受购物车功能影响

## 技术债务

- 考虑将搜索功能集成到 `website_artextile_jsi` 中以减少模块数量
- 评估是否需要订单级别和订单行级别的 customer PO 功能并存
