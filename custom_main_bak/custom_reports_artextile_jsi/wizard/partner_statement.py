import base64
import io
from collections import defaultdict
from datetime import date

from odoo import fields, models
from odoo.osv.expression import AND
from odoo.tools.misc import xlsxwriter


class ResPartnerStatementReport(models.TransientModel):
    _name = "res.partner.statement.report"
    _description = "Partner Statement Report"

    partner_id = fields.Many2one("res.partner", "Customers")
    start_date = fields.Date("From Date")
    end_date = fields.Date("To Date")
    report_type = fields.Selection([("xlsx", "Excel"), ("pdf", "PDF")], string="Print-out Type", default="pdf")
    file_data = fields.Binary(readonly=True, attachment=False)

    def generate_report(self):
        # Address remove ', ' if street2 is blank
        address = self.partner_id.street
        if self.partner_id.street2:
            if address:
                address = "%s, %s" % (address, self.partner_id.street2)
            else:
                address = self.partner_id.street2
        # Header
        header_data = {
            "start_date": date.strftime(self.start_date, "%d/%B/%Y"),
            "end_date": date.strftime(self.end_date, "%d/%B/%Y"),
            "full_name": self.partner_id.full_name,
            "address": address,
            "phone": self.partner_id.phone,
            "fax": self.partner_id.fax,
            "code": self.partner_id.code,
            "payment_term": self.partner_id.property_payment_term_id.name,
        }

        # Table1: Invoices of Current Period
        current_preriod_data = self.prepare_current_preriod_data()

        # Table2: Due invoices Summary
        due_invoice_data = defaultdict(lambda: {"sum": 0})
        summary_data = self.prepare_summary_data()
        for line in summary_data:
            due_invoice_data[line["month"]]["sum"] += line["amount_residual"]
        if self.report_type == "pdf":
            return (
                self.env.ref("custom_reports_artextile_jsi.action_report_partner_statement_artextile_jsi")
                .with_context(
                    header_data=header_data,
                    current_preriod_data=current_preriod_data,
                    due_invoice_data=due_invoice_data,
                )
                .report_action([], data={"data": {}}, config=False)
            )
        else:
            self.prepare_excel_report(header_data, current_preriod_data, due_invoice_data)
            return {
                "type": "ir.actions.act_url",
                "url": "/web/content/?model={}&id={}&field=file_data&filename={}&download=true".format(
                    self._name, self.id, "PartnerStatement-%s to %s.xlsx" % (self.start_date, self.end_date)
                ),
                "target": "self",
            }

    def get_total_paid_amount(self, aml):
        """
        Calculate Paid amount againts Account Move
        Inspired: https://github.com/odoo/odoo/blob/14.0/addons/account/models/account_move.py#L2311
        """
        paid_amount = 0
        if not aml:
            return paid_amount
        for partial in aml.matched_credit_ids:
            if partial.credit_move_id.date <= self.end_date:
                paid_amount += partial.credit_amount_currency
        for partial in aml.matched_debit_ids:
            if partial.debit_move_id.date <= self.end_date:
                paid_amount += partial.debit_amount_currency
        return paid_amount

    def get_move_data(self, move, amount_total, amount_residual, is_debit):
        """
        Prepare dict. for move
        """
        return {
            "invoice_date": date.strftime(move.date, "%d/%m/%Y"),
            "name": (move.invoice_origin or move.name) if move.move_type == "out_invoice" else move.name,
            # By FWO: make journal entry to show positive based on debit
            "amount_total": -amount_total if not is_debit and move.move_type in ["entry", "out_refund"] else amount_total,
            "amount_residual": -amount_residual if not is_debit and move.move_type in ["entry", "out_refund"] else amount_residual,
            "invoice_date_due": date.strftime(move.invoice_date_due or move.date, "%d/%m/%Y"),
            "month": date.strftime(move.date, "%m/%Y"),
        }

    def prepare_move_data(self, moves):
        """
        Prepare Move-wise list of dict
        """
        move_data = []
        for move in moves:
            paid_amount = round(self.get_total_paid_amount(move.line_ids), 2)
            amount_total = 0.0
            is_debit = False
            # By FWO
            # if it's customer Invoice (out_invoice), take amount_total field directly
            # else Move may be receivable, payable, liquidity and other so, only take it from receivable
            if move.move_type == "out_invoice":
                amount_total = move.amount_total
                amount_residual = amount_total - paid_amount
                # if Remaining amount is 0, no need to print it in report
                if not amount_residual:
                    continue
                move_data.append(self.get_move_data(move, amount_total, amount_residual, is_debit))
            else:
                # If the move type is not out_invoice, need to check all the journal items line by line.
                # Cannot sum all the move line together
                for line in move.line_ids:
                    if line.account_type == "asset_receivable":
                        amount_total = round(line.credit, 2)
                        if amount_total == 0.0:
                            amount_total = round(line.debit, 2)
                            is_debit = True
                        paid_amount = round(self.get_total_paid_amount(line), 2)
                        amount_residual = amount_total - paid_amount
                        # if Remaining amount is 0, no need to print it in report
                        if not amount_residual:
                            continue
                        move_data.append(self.get_move_data(move, amount_total, amount_residual, is_debit))

        return move_data

    def get_moves(self, extra_domain):
        domain = AND(
            [
                extra_domain,
                [
                    ("partner_id", "=", self.partner_id.id),
                    ("date", "<=", self.end_date),
                    ("move_id.state", "=", "posted"),
                    ("display_type", "not in", ["line_section", "line_note"]),
                    ("account_type", "=", "asset_receivable"),
                ],
            ]
        )
        return self.env["account.move.line"].search(domain, order="date").move_id

    def prepare_current_preriod_data(self):
        """
        Table1: Invoices of Current Period
        """
        extra_domain = [("date", ">=", self.start_date)]
        moves = self.get_moves(extra_domain)
        return self.prepare_move_data(moves)

    def prepare_summary_data(self):
        """
        Table2: Due invoices Summary
        """
        moves = self.get_moves([])
        return self.prepare_move_data(moves)

    def prepare_excel_report(self, header_data, current_preriod_data, due_invoice_data):
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {"in_memory": True})
        bold = workbook.add_format({"bold": True})
        worksheet = workbook.add_worksheet()
        worksheet.write(2, 1, self.env.company.name, bold)
        worksheet.write(3, 1, self.env.company.street)
        company_address = " ".join([self.env.company.street2, self.env.company.city, self.env.company.state_id.name, self.env.company.country_id.name])
        worksheet.write(3, 1, company_address)
        worksheet.write(4, 1, ("Tel: %s  Fax: %s" % (header_data["phone"], header_data["fax"])), bold)
        worksheet.write(5, 1, ("Period 期間 : From ⾃ %s To ⾄ %s" % (header_data["start_date"], header_data["end_date"])), bold)

        worksheet.write(7, 1, "Customer : ", bold)
        worksheet.write(7, 2, header_data["full_name"])
        worksheet.write(7, 3, "Customer Code : ", bold)
        worksheet.write(7, 4, header_data["code"])

        worksheet.write(8, 1, "Address : ", bold)
        worksheet.write(8, 2, header_data["address"])
        worksheet.write(8, 3, "Payment Terms : ", bold)
        worksheet.write(8, 4, header_data["payment_term"])

        worksheet.write(9, 1, "Tel : ", bold)
        worksheet.write(9, 2, header_data["phone"])
        worksheet.write(9, 3, "Currency : ", bold)
        worksheet.write(9, 4, "HKD")

        worksheet.write(10, 1, "Fax : ", bold)
        worksheet.write(10, 2, header_data["fax"])

        worksheet.write(12, 1, "Invoices of Current Period 本期交易：", bold)
        row = 14
        table_current_preriod_header = [
            "Inv. Date (d/m/y) 發票⽇期(⽇/⽉/年)",
            "Invoice No. 發票單號",
            "Invoice Amt. 發票⾦額 (HKD)",
            "Remaining Amt. 結餘⾦額 (HKD)",
        ]
        col = 0
        for head in table_current_preriod_header:
            worksheet.write(row, col, head, bold)
            col += 1
        row += 1
        total_residual_amount = 0.0
        for line in current_preriod_data:
            worksheet.write(row, 0, line["invoice_date"])
            worksheet.write(row, 1, line["name"])
            worksheet.write(row, 2, "%.2f" % line["amount_total"])
            worksheet.write(row, 3, "%.2f" % line["amount_residual"])
            total_residual_amount += line["amount_residual"]
            row += 1
        worksheet.write(row, 3, "%.2f" % total_residual_amount, bold)
        row += 3
        worksheet.write(row, 1, "Summary 總結：", bold)
        row += 1
        table_due_invoice_header = [
            "Monthly Inv. 發票⽉份",
            "Remaining Amt. ⽋款⾦額 (HKD)",
        ]
        col = 0
        for head in table_due_invoice_header:
            worksheet.write(row, col, head, bold)
            col += 1
        row += 1
        total_residual_amount = 0.0
        for line in due_invoice_data:
            worksheet.write(row, 0, line)
            worksheet.write(row, 1, "%.2f" % due_invoice_data[line]["sum"])
            total_residual_amount += due_invoice_data[line]["sum"]
            row += 1
        worksheet.write(row, 1, "%.2f" % total_residual_amount, bold)

        workbook.close()
        xlsx_data = output.getvalue()
        self.file_data = base64.encodebytes(xlsx_data)
        return True
