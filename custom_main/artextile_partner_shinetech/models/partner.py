from odoo import _, api, fields, models
from odoo.exceptions import ValidationError


class Partner(models.Model):
    _inherit = "res.partner"

    def open_ar_partner_ledger(self):
        action = self.env["ir.actions.client"]._for_xml_id(
            "account_reports.action_account_report_partner_ledger"
        )
        action["params"] = {
            "options": {
                "partner_ids": [self.id],
                "date": {"mode": "range", "filter": "this_month"},
                "unfold_all": True,
            },
            "ignore_session": True,
        }
        return action
