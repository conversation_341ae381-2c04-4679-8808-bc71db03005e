# Ps-Tech module : Product misc - jsi

## ********.0[OdooHK-jsi]

1. Weight related fields on product.

## ********.1[OdooHK-jsi]

1. Quantity related fields on product.

## ********.3[OdooHK-jsi]

1. Misc fields on product.
2. Smart buttons
3. Search product by vendor product on POL

## ********.4[OdooHK-jsi]

1. Search by Version Family

## ********.5[OdooHK-jsi]

1. Refactor product from, fields and onchange
2. Remove unnecessary view and menu

## ********.6

-   Update version product from Tree and form view

## ********.7

-   Add component as master product of the main product on bom

## ********.8

-   Allow mass version product updation from the master product on tree view

## ********.9

-   Add new inherited product form view from the bom to avoid access right error

## ********.10

-   Make reserved_availability field as store=True

## ********.11

-   Form view fields changes

## ********.12

-   Make compute field searchable
-   Refactored existing compute method and reuse the existing code
-   Stop updating brand from master product

## ********.13
-   Redesign Re-stock status and Re-stock date field

## ********.14
-   Updating Ecom Remarks, Availability and Availability Threshold from master product
-   fix qty_on_po search: Compare given values with result of read_group instead of passing it to domain

## ********.15
-   Added Lab related fields on product and sync with version
-   Remove `name` from the sync
-   Ranking management
-   Change the fields position on product form

## ********.16
-    Added `shipping_info` on `product.template` and synch with master product

## ********.17
-    Added `product_feature` on `product.template` and synch with master product

## ********.0
-    Migration from 14.0 to 17.0

## ********.1
-    Remove `readonly` from `Name` on `Version product`(It was done using studio in v14)

## ********.2
-    New Checkbox `Cutting Location?` on `stock.location`
