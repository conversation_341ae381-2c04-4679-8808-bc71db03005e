from . import models

from datetime import date, timedelta
from odoo import api, SUPERUSER_ID


def _sequence_lot_name_date_range_post_init(env):
    """
    Generate Past date_range for Lot name sequence.
    """
    seq_lot_name = env.ref("lot_cust_jsi.sequence_lot_name_lot_cust_jsi")
    today = date.today()
    seq_lot_name.write(
        {
            "date_range_ids": [
                (
                    0,
                    0,
                    {
                        "date_from": today + timedelta(days=i),
                        "date_to": today + timedelta(days=i + 1),
                        "number_next_actual": 1,
                    },
                )
                for i in range(3000)
            ]
        }
    )
