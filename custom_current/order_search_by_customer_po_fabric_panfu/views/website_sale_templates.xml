<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--
    Note: Customer PO functionality is now handled by website_artextile_jsi module
    at the order line level. This template is disabled to avoid conflicts.
    The website_artextile_jsi module provides line-level customer PO fields
    which are more flexible than order-level fields.
    -->

    <!-- Disabled to avoid conflict with website_artextile_jsi -->
    <!--
    <template id="cart_lines_customer_po" inherit_id="website_sale.cart_lines" name="Cart Lines with Customer PO">
        <xpath expr="//div[@id='cart_products']" position="inside">
            <div class="input-group mb-3" t-if="website_sale_order and website_sale_order.website_order_line">
                <span class="input-group-text">客户采购单号:</span>
                <input type="text" class="form-control customer_po"
                       t-att-value="website_sale_order.customer_po or ''"
                       placeholder="请输入客户采购单号"/>
            </div>
        </xpath>
    </template>
    -->
</odoo>