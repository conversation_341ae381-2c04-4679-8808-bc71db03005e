#!/usr/bin/env python3
"""
Compatibility test for order_search_by_customer_po_fabric_panfu module
This script tests if the module can coexist with website_artextile_jsi
"""

import sys
import os

def test_module_compatibility():
    """Test if both modules can be loaded without conflicts"""
    print("Testing module compatibility...")
    
    # Check if both modules exist
    # Get the artx directory (current working directory)
    artx_dir = os.getcwd()

    custom_main_path = os.path.join(artx_dir, 'custom_main', 'website_artextile_jsi')
    custom_current_path = os.path.join(artx_dir, 'custom_current', 'order_search_by_customer_po_fabric_panfu')
    
    print(f"Checking custom_main path: {custom_main_path}")
    print(f"Checking custom_current path: {custom_current_path}")
    
    if not os.path.exists(custom_main_path):
        print("❌ website_artextile_jsi module not found in custom_main")
        return False
        
    if not os.path.exists(custom_current_path):
        print("❌ order_search_by_customer_po_fabric_panfu module not found in custom_current")
        return False
    
    print("✅ Both modules found")
    
    # Check JavaScript files
    js_files = [
        os.path.join(custom_current_path, 'static', 'src', 'js', 'customer_po.js'),
        os.path.join(custom_current_path, 'static', 'src', 'js', 'portal_search.js'),
        os.path.join(custom_main_path, 'static', 'src', 'js', 'website_sale.js')
    ]
    
    for js_file in js_files:
        if os.path.exists(js_file):
            print(f"✅ Found: {os.path.basename(js_file)}")
            # Check if it uses new syntax
            with open(js_file, 'r') as f:
                content = f.read()
                if '/** @odoo-module **/' in content:
                    print(f"  ✅ Uses Odoo 17 syntax")
                elif 'odoo.define(' in content:
                    print(f"  ⚠️  Uses old Odoo syntax")
        else:
            print(f"❌ Missing: {js_file}")
    
    print("\n✅ Compatibility test completed successfully!")
    print("Both modules should now work together without JavaScript conflicts.")
    return True

if __name__ == "__main__":
    success = test_module_compatibility()
    sys.exit(0 if success else 1)
