from odoo import _, api, fields, models
from odoo.exceptions import UserError


class ResConfigSettings(models.TransientModel):
    _inherit = "res.config.settings"

    lock_end_date = fields.Date(related="company_id.lock_end_date", readonly=False)
    lock_start_date = fields.Date(related="company_id.lock_start_date", readonly=False)
    lock_end_date_non_adviser = fields.Date(related="company_id.lock_end_date_non_adviser", readonly=False)
    lock_start_date_non_adviser = fields.Date(related="company_id.lock_start_date_non_adviser", readonly=False)

    @api.constrains("lock_start_date", "lock_end_date")
    def _check_lock_entries(self):
        if not self.user_has_groups("account.group_account_manager"):
            raise UserError(_("Only Billing Administrators are allowed to change lock dates!"))
        for record in self:
            is_both_lock_date_set = record.lock_start_date and record.lock_end_date
            if not is_both_lock_date_set and (record.lock_start_date or record.lock_end_date):
                raise UserError(_("Please fill in both lock dates."))
            if is_both_lock_date_set and record.lock_start_date > record.lock_end_date:
                raise UserError(_("Lock start date must be earlier than lock end date."))

    @api.constrains("lock_start_date_non_adviser", "lock_end_date_non_adviser")
    def _check_lock_entries_for_non_advisers(self):
        if not self.user_has_groups("account.group_account_manager"):
            raise UserError(_("Only Billing Administrators are allowed to change lock dates!"))
        for record in self:
            is_both_lock_date_set = record.lock_start_date_non_adviser and record.lock_end_date_non_adviser
            if not is_both_lock_date_set and (record.lock_start_date_non_adviser or record.lock_end_date_non_adviser):
                raise UserError(_("Please fill in both lock dates for non-advisers."))
            if is_both_lock_date_set and record.lock_start_date_non_adviser > record.lock_end_date_non_adviser:
                raise UserError(
                    _("Lock start date for non-advisers must be earlier than lock end date for non-advisers.")
                )
