from odoo import _, api, fields, models
from odoo.exceptions import ValidationError


class Partner(models.Model):
    _inherit = "res.partner"

    def open_ar_partner_ledger(self):
        return {
            "type": "ir.actions.client",
            "name": _("Partner Ledger"),
            "tag": "account_report",
            "params": {
                "options": {"partner_ids": [self.id]},
                "ignore_session": "both",
            },
            "context": {
                "report_id": self.env.ref("account_reports.partner_ledger_report").id
            },
        }
