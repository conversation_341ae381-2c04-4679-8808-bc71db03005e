<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="action_mass_reservation_delivery_automation_jsi" model="ir.actions.server">
            <field name="name">Mass Reservation</field>
            <field name="model_id" ref="stock.model_stock_picking" />
            <field name="binding_model_id" ref="stock.model_stock_picking" />
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
# Need to reserve quantity only if show_check_availability is True
allowed_picking = records.filtered(lambda picking: picking.show_check_availability)
if allowed_picking:
    allowed_picking.action_assign()
            </field>
        </record>
    </data>
</odoo>
