from odoo import _, api, fields, models
from odoo.exceptions import ValidationError


class ResConfigSettings(models.TransientModel):
    _inherit = "res.config.settings"

    use_allowed_max_sale_qty = fields.Boolean(
        related="company_id.use_allowed_max_sale_qty", string="Restrict sale quantity per line in website", readonly=False
    )
    allowed_max_sale_qty = fields.Float(related="company_id.allowed_max_sale_qty", string="Maximum Sale Quantity", readonly=False)
    stock_qty_threshold = fields.Float(related="company_id.stock_qty_threshold", string="Stock Quantity Threshold", readonly=False)

    @api.constrains('use_allowed_max_sale_qty', 'allowed_max_sale_qty')
    def _check_use_allowed_max_sale_qty(self):
        if self.use_allowed_max_sale_qty:
            if self.allowed_max_sale_qty <= 0:
                raise ValidationError(
                    _('Allowed Max. Quantity should be positive (i.e. >0) !')
                )
