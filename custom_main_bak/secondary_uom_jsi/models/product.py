from odoo import api, fields, models, tools


class ProductTemplate(models.Model):
    _inherit = "product.template"

    secondary_uom_qty = fields.Float("Secondary qty", compute="_compute_secondary_uom_qty")
    secondary_uom_id = fields.Many2one("uom.uom", "Secondary uom")
    secondary_uom_name = fields.Char("Secondary uom name", related="secondary_uom_id.name")
    secondary_uom_list_price = fields.Float("Price (Y)", digits="Product Price")
    list_price = fields.Float(compute="_compute_list_price", readonly=False, store=True)

    @api.depends("secondary_uom_id", "secondary_uom_list_price")
    def _compute_list_price(self):
        for template in self:
            if template.secondary_uom_id and template.uom_id:
                template.list_price = template.secondary_uom_id._compute_price(
                    template.secondary_uom_list_price, template.uom_id
                )

    def _compute_secondary_uom_qty(self):
        for template in self:
            secondary_uom_qty = 0.0
            if template.secondary_uom_id:
                secondary_uom_qty = template.uom_id._compute_quantity(
                    template.qty_available, template.secondary_uom_id, rounding_method="HALF-UP"
                )
            template.secondary_uom_qty = secondary_uom_qty


class ProductProduct(models.Model):
    _inherit = "product.product"

    def get_product_multiline_description_sale(self):
        """
        Overriding to remove Internal Reference from the name.
        Generally used for get Sale Description field from Product.
        It's fine to remove directly as client dont want Internal Reference.
        """
        name = self.name
        if self.description_sale:
            name += "\n" + self.description_sale
        return name
