import operator as py_operator
from collections import defaultdict

from odoo import api, fields, models, tools
from odoo.osv.expression import AND
from odoo.tools import float_round

OPERATORS = {
    "<": py_operator.lt,
    ">": py_operator.gt,
    "<=": py_operator.le,
    ">=": py_operator.ge,
    "=": py_operator.eq,
    "!=": py_operator.ne,
}

READONLY_FOR_VERSION = ['width_cm', 'product_feature', 'region_of_origin_id', 'ecom_remarks', 'stock_availability', 'quick_service', 'keep_stock', 'same_product_ids', 'high_similar_ids', 'med_similar_ids', 'low_similar_ids', 'secondary_uom_id', 'uom_id', 'uom_po_id', 'attribute_details', 'lab_origin', 'lab_content', 'lab_super', 'lab_width', 'lab_feature', 'lab_colour', 'product_colour_ids', 'product_pattern_ids', 'product_compo_ids', 'product_yarn_id', 'solids', 'double_face', 'sub_categ_id', 'master_collection_id', 'allow_out_of_stock_order', 'detailed_type', 'low_weight_g', 'high_weight_g', 'hs_code']
REQUIRED_FOR_DEV = ['brand_id', 'width_cm', 'region_of_origin_id', 'quick_service', 'keep_stock', 'product_colour_ids', 'product_pattern_ids', 'product_compo_ids', 'collection_id', 'product_yarn_id', 'master_collection_id']
M2O_M2M_NO_CREATE = ['brand_id', 'region_of_origin_id', 'same_product_ids', 'high_similar_ids', 'med_similar_ids', 'low_similar_ids', 'product_colour_ids', 'product_pattern_ids', 'product_compo_ids', 'collection_id', 'product_yarn_id', 'sub_categ_id', 'master_collection_id', 'related_sample_book_ids']


class StockMove(models.Model):
    _inherit = "stock.move"

    """
        2 reason for making reserved_availability field as store=True
        - When we have Demand quantity = 1.85, odoo compute reserved_availability on the fly and make it like 1.849999
          Due to this, it break few custom development
        - Getting value in read_group for our custom compute fields
    """
    reserved_availability = fields.Float(store=True)


class ProductProduct(models.Model):
    _inherit = "product.product"

    # With the simple SQL constraints we can not check case insensitive unique
    def init(self):
        self._cr.execute(
            """CREATE UNIQUE INDEX IF NOT EXISTS unique_default_code ON product_product(UPPER(default_code))"""
        )


class ProductTemplate(models.Model):
    _inherit = "product.template"

    @tools.ormcache()
    def _get_default_uom_id(self):
        # Overriding to change default UOM Unit to Meter
        return self.env.ref("uom.product_uom_meter")

    uom_id = fields.Many2one("uom.uom", default=_get_default_uom_id)
    uom_po_id = fields.Many2one("uom.uom", default=_get_default_uom_id)
    is_master = fields.Boolean("Master?", default="True")
    master_product_id = fields.Many2one("product.template", "Master Product", tracking=True)
    master_no = fields.Char(compute="_compute_master_no")
    version_family = fields.Char(compute="_compute_version_family", search="_search_version_family_name")
    # Weight fields
    low_weight_g = fields.Float("Low weight (g)")
    high_weight_g = fields.Float("high weight (g)")
    width_cm = fields.Float("Width (cm)")
    average_weight_g = fields.Float("weight (g)", compute="_compute_all_weight", compute_sudo=True)
    weight_gm2 = fields.Float("Weight (g/m2)", compute="_compute_all_weight", compute_sudo=True)
    weight_range_g = fields.Selection([
        ("0_89", "0-89"),
        ("90_189", "90-189"),
        ("190_259", "190-259"),
        ("260_309", "260-309"),
        ("gte_310", ">=310"),
        ("n_a", "N/A")], "Weight range (g)", compute="_compute_all_weight", compute_sudo=True)
    # Qty fields
    qty_on_cutting = fields.Float(
        "Cutting", compute="_compute_qty", digits="Product Unit of Measure", search="_search_qty_on_cutting"
    )
    qty_on_stock = fields.Float(
        "Stock", compute="_compute_qty", digits="Product Unit of Measure", search="_search_qty_on_stock"
    )
    qty_on_po = fields.Float(
        "PO Qty", compute="_compute_qty", digits="Product Unit of Measure", search="_search_qty_on_po"
    )
    qty_total = fields.Float(
        "Free Inventory", compute="_compute_qty", digits="Product Unit of Measure", search="_search_qty_total"
    )
    qty_on_so = fields.Float("Pending SO", compute="_compute_qty_on_so", digits="Product Unit of Measure")

    # Misc fields
    same_product_ids = fields.Many2many("product.template", "product_template_same_products_rel", "col1", "col2")
    high_similar_ids = fields.Many2many("product.template", "product_template_high_products_rel", "col1", "col2")
    med_similar_ids = fields.Many2many("product.template", "product_template_med_products_rel", "col1", "col2")
    low_similar_ids = fields.Many2many("product.template", "product_template_low_products_rel", "col1", "col2")
    related_sample_book_ids = fields.Many2many(
        "product.template", "product_template_same_books_products_rel", "col1", "col2"
    )
    version_product_ids = fields.One2many("product.template", "master_product_id", "Version Products")
    product_colour_ids = fields.Many2many("product.colour")
    product_compo_ids = fields.Many2many("product.composition")
    product_pattern_ids = fields.Many2many("product.pattern")
    master_collection_id = fields.Many2one("product.collection", "Master Collection")
    region_of_origin_id = fields.Many2one("res.country")
    collection_id = fields.Many2one("product.collection", "Collection")
    product_yarn_id = fields.Many2one("product.yarn.count", "Super_Micron")
    sub_categ_id = fields.Many2one("product.subcategory")
    brand_id = fields.Many2one("product.brand", "Brand")
    collection_name_full = fields.Char(related="collection_id.name_full")
    attribute_details = fields.Char("Internal Remark")
    vendor_item_no = fields.Char(compute="_compute_vendor_item_no", store="True", recursive=True)
    equiv_super_lv = fields.Char("Equiv Super LV", related="product_yarn_id.yarn_count")
    is_development = fields.Boolean(related="categ_id.is_development")
    sample_bookcat = fields.Char("Sample Book", related="categ_id.name")
    nav_no = fields.Char("NAV no.")
    ecom_remarks = fields.Text("Ecom Remarks", translate=True)
    double_face = fields.Boolean("Double Face?")
    solids = fields.Boolean("Solids?")
    stock_availability = fields.Selection([
        ("low_stock", "Low Stock"),
        ("temp_out", "Re-stocking"),
        ("oos", "Dropped")], string="Re-stock status")
    quick_service = fields.Selection([("yes", "Yes"), ("n_a", "n/a")], "Quick Service?", default="n_a")
    keep_stock = fields.Selection([("yes", "Yes"), ("no", "No")], "Keep Stock?")
    temp_out_until = fields.Date("Re-stock date", compute="_compute_temp_out_until", store=True, readonly=False)
    shipping_info = fields.Char("Shipping Info", translate=True)
    product_feature = fields.Text("Feature", translate=True)

    # Lab related fields
    lab_origin = fields.Char("Lab Origin")
    lab_content = fields.Char("Lab Content")
    lab_super = fields.Char("Lab Super")
    lab_weight = fields.Char("Lab Weight")
    lab_width = fields.Char("Lab Width")
    lab_feature = fields.Char("Lab Feature")
    lab_colour = fields.Char("Lab Colour")
    rank_number = fields.Char("Rank Number")
    rank = fields.Boolean("Rank")

    @api.depends("stock_availability")
    def _compute_temp_out_until(self):
        """
            if stock_Availability is blank or oos then make temp_out_until blank
        """
        for product in self:
            if product.stock_availability in [False, 'oos']:
                product.temp_out_until = False

    @api.depends("low_weight_g", "high_weight_g", "width_cm")
    def _compute_all_weight(self):
        for product in self:
            weight_gm2 = 0.0
            average_weight_g = (product.low_weight_g + product.high_weight_g) / 2
            weight = average_weight_g / 1000
            product.product_variant_ids.weight = weight
            if not (product.low_weight_g or product.high_weight_g):
                weight_range_g = "n_a"
            elif average_weight_g <= 89:
                weight_range_g = "0_89"
            elif average_weight_g <= 189:
                weight_range_g = "90_189"
            elif average_weight_g <= 259:
                weight_range_g = "190_259"
            elif average_weight_g <= 309:
                weight_range_g = "260_309"
            elif average_weight_g >= 310:
                weight_range_g = "gte_310"

            if product.width_cm:
                weight_gm2 = average_weight_g / (product.width_cm / 100)
            product.update(
                {
                    "average_weight_g": average_weight_g,
                    "weight_gm2": weight_gm2,
                    "weight_range_g": weight_range_g,
                }
            )

    @api.depends("qty_available", "master_product_id")
    def _compute_qty(self):
        master_products = self.filtered(lambda p: not p.master_product_id)
        incoming_domain = [("picking_code", "=", "incoming"), ("location_id.usage", "=", "supplier")]
        incoming_stock_moves = self.get_moves(master_products.ids, incoming_domain)
        incoming_stock_moves_data = defaultdict(lambda: 0)
        for ml in incoming_stock_moves:
            incoming_stock_moves_data[ml["product_id"][0]] = ml["product_uom_qty"]
        if master_products:
            master_data = self._prepare_template_data(master_products)
            for product, pdata in master_data.items():
                product.update(
                    {
                        "qty_on_cutting": pdata[0],
                        "qty_on_stock": pdata[1],
                        "qty_on_po": incoming_stock_moves_data[product.product_variant_ids[:1].id],
                        "qty_total": pdata[0] + pdata[1],
                    }
                )
        for product in self.filtered(lambda p: p.master_product_id):
            product.update(
                {
                    "qty_on_cutting": product.master_product_id.qty_on_cutting,
                    "qty_on_stock": product.master_product_id.qty_on_stock,
                    "qty_on_po": product.master_product_id.qty_on_po,
                    "qty_total": product.master_product_id.qty_total,
                }
            )

    def get_moves(self, product_ids, extra_domain):
        domain = AND(
            [
                extra_domain,
                [
                    ("product_id.product_tmpl_id", "in", product_ids),
                    ("state", "in", ["confirmed", "partially_available", "assigned"]),
                ],
            ]
        )
        return self.env["stock.move"].read_group(domain, ["product_uom_qty", "reserved_availability"], ["product_id"])

    def _compute_qty_on_so(self):
        moves = self.env["stock.move"].read_group(
            [
                ("picking_code", "=", "outgoing"),
                ("product_id.product_tmpl_id", "in", self.ids),
                ("state", "not in", ["done", "cancel"]),
            ],
            ["product_uom_qty"],
            ["product_id"],
        )
        moves_data = defaultdict(lambda: 0)
        for move in moves:
            moves_data[move["product_id"][0]] = move["product_uom_qty"]
        for product in self:
            product.qty_on_so = moves_data[product.product_variant_ids[:1].id]

    def _search_qty_on_po(self, operator, value):
        if operator not in ("<", "=", ">", ">=", "<="):
            return []
        if type(value) not in (float, int):
            return []
        # Need to search only master product
        products = self.search([("master_product_id", "=", False)])
        incoming_domain = [
            ("picking_code", "=", "incoming"),
            ("location_id.usage", "=", "supplier"),
        ]
        incoming_stock_moves = self.get_moves(products.ids, incoming_domain)
        tmpl_ids = []
        if incoming_stock_moves:
            product_ids = [line["product_id"][0] for line in incoming_stock_moves if OPERATORS[operator](line['product_uom_qty'], value)]
            # as stock.move return product.product, we need to find its related template
            tmpl_ids = self.env["product.product"].browse(product_ids).product_tmpl_id.ids
        return ["|", ("id", "in", tmpl_ids), ("master_product_id", "in", tmpl_ids)]

    def _search_qty_on_cutting(self, operator, value):
        return self._search_products("qty_on_cutting", operator, value)

    def _search_qty_on_stock(self, operator, value):
        return self._search_products("qty_on_stock", operator, value)

    def _search_qty_total(self, operator, value):
        return self._search_products("qty_total", operator, value)

    def _search_products(self, field, operator, value):
        if operator not in ("<", "=", ">", ">=", "<="):
            return []
        if type(value) not in (float, int):
            return []
        templates = self.search([("master_product_id", "=", False)])
        variant_data = self._prepare_template_data(templates)
        tmpl_ids = []
        for product, pdata in variant_data.items():
            if field == "qty_on_cutting":
                qty_on_cutting = float_round(pdata[0], precision_digits=2)
                if OPERATORS[operator](qty_on_cutting, value):
                    tmpl_ids.append(product.id)
            elif field == "qty_on_stock":
                qty_on_stock = float_round(pdata[1], precision_digits=2)
                if OPERATORS[operator](qty_on_stock, value):
                    tmpl_ids.append(product.id)
            elif field == "qty_total":
                qty_total = float_round((pdata[0] + pdata[1]), precision_digits=2)
                if OPERATORS[operator](qty_total, value):
                    tmpl_ids.append(product.id)
        return ["|", ("id", "in", tmpl_ids), ("master_product_id", "in", tmpl_ids)]

    def _prepare_template_data(self, templates):
        """Helper method to prepare template data"""
        cutting_location = self.env["stock.location"]._get_cutting_location()

        cutting_ongoing_domain = [
            ("location_dest_id.usage", "in", ["customer", "production"]),
            ("location_id", "=", cutting_location.id),
        ]
        cutting_ongoing_stock_moves = self.get_moves(templates.ids, cutting_ongoing_domain)

        non_cutting_ongoing_domain = [
            ("location_dest_id.usage", "in", ["customer", "production"]),
            ("location_id", "!=", cutting_location.id),
        ]
        non_cutting_ongoing_stock_moves = self.get_moves(templates.ids, non_cutting_ongoing_domain)

        return_domain = [("location_dest_id.usage", "=", "supplier")]
        return_stock_moves = self.get_moves(templates.ids, return_domain)

        cutting_demand_reserved_qty_data = defaultdict(lambda: [0, 0])
        non_cutting_demand_reserved_qty_data = defaultdict(lambda: [0, 0])
        return_qty_data = defaultdict(lambda: 0)

        for ml in cutting_ongoing_stock_moves:
            cutting_demand_reserved_qty_data[ml["product_id"][0]] = [
                ml["product_uom_qty"],
                ml["reserved_availability"],
            ]
        for ml in non_cutting_ongoing_stock_moves:
            non_cutting_demand_reserved_qty_data[ml["product_id"][0]] = [
                ml["product_uom_qty"],
                ml["reserved_availability"],
            ]
        for ml in return_stock_moves:
            return_qty_data[ml["product_id"][0]] = ml["reserved_availability"]

        product_data = {}
        for product in templates:
            variant_id = product.product_variant_ids[:1].id
            available_cutting_qty = product.with_context(location=cutting_location.id).qty_available
            product.invalidate_recordset(fnames=['qty_available'])
            cutting_demand_qty = cutting_demand_reserved_qty_data[variant_id][0]
            cutting_reserved_qty = cutting_demand_reserved_qty_data[variant_id][1]
            demand_qty = non_cutting_demand_reserved_qty_data[variant_id][0]
            reserved_qty = non_cutting_demand_reserved_qty_data[variant_id][1]
            qty_on_cutting = available_cutting_qty - cutting_reserved_qty - (cutting_demand_qty - cutting_reserved_qty)
            qty_on_stock = (
                product.qty_available
                - available_cutting_qty
                - return_qty_data[variant_id]
                - reserved_qty
                - (demand_qty - reserved_qty)
            )
            product_data[product] = [qty_on_cutting, qty_on_stock]
        return product_data

    @api.depends("type")
    def _compute_master_no(self):
        for product in self:
            if not product.bom_ids:
                product.master_no = product.default_code
            else:
                product.master_no = (
                    product.bom_ids.bom_line_ids and product.bom_ids.bom_line_ids[0].product_id.default_code or ""
                )

    @api.depends("seller_ids.product_code", "master_product_id.vendor_item_no")
    def _compute_vendor_item_no(self):
        """
        Client will add seller data on Master product only.
        By default all version products are `Is purchase=False`, So no way to add seller data.
        """
        for product in self:
            if product.master_product_id:
                # This part is for version product
                product.vendor_item_no = product.master_product_id.vendor_item_no
            else:
                # This part is for master product
                product.vendor_item_no = product.seller_ids and product.seller_ids[0].product_code or ""

    @api.depends("master_product_id", "default_code")
    def _compute_version_family(self):
        for product in self:
            if product.master_product_id:
                version_family = product.master_product_id.version_family
            else:
                default_codes = [product.default_code or ""]
                if product.version_product_ids:
                    default_codes.extend(
                        product.version_product_ids.sorted(key=lambda p: p.create_date).mapped("default_code")
                    )
                version_family = "%s" % (", ".join(default_codes))
            product.version_family = version_family

    def _search_version_family_name(self, operator, value):
        """
        Here, customer want to search by version_family.
        Version_family is a combination of default_code of master_product_id and its version_product_ids.
        We can search first default_code, and then will combine its master and version products,
        """
        domain = [("default_code", operator, value)]
        products = self.search(domain)
        product_ids = products.ids
        if products:
            """
            Check if products, has any
                - products with is_master, gether it's version_product_ids
                - products with master_product_id, gether version_product_ids
            """
            master_products = products.filtered(lambda x: x.is_master)
            master_products += products.master_product_id
            if master_products:
                product_ids += master_products.ids + master_products.version_product_ids.ids
        return [("id", "in", product_ids)]

    @api.onchange("is_master")
    def onchange_is_master(self):
        for product in self:
            if not product.is_master:
                product.update(
                    {
                        "route_ids": [(4, self.env.ref("mrp.route_warehouse0_manufacture").id)],
                        "purchase_ok": False,
                        "tracking": "none",
                    }
                )
            else:
                product.update(
                    {
                        "purchase_ok": True,
                        "route_ids": [(4, self.env.ref("purchase_stock.route_warehouse0_buy").id)],
                    }
                )

    @api.onchange("is_development", "is_master", "type")
    def onchange_is_master_is_development(self):
        for product in self:
            if product.type == "product" and product.is_master and product.is_development:
                product.tracking = "lot"

    @api.onchange("master_product_id")
    def onchange_master_product_id(self):
        for product in self:
            if not product.master_product_id:
                continue
            product.update_version_products(product.master_product_id)
            product.rank_number = product.master_product_id.rank_number

    def update_version_products(self, master_product):
        self.update(
            {
                "detailed_type": master_product.detailed_type,
                "hs_code": master_product.hs_code,
                "description": master_product.description,
                "image_1920": master_product.image_1920,
                "secondary_uom_id": master_product.secondary_uom_id.id,
                "uom_id": master_product.uom_id.id,
                "uom_po_id": master_product.uom_po_id.id,
                "allow_out_of_stock_order": master_product.allow_out_of_stock_order,
                "standard_price": master_product.standard_price,
                "same_product_ids": [(6, 0, master_product.same_product_ids.ids)],
                "high_similar_ids": [(6, 0, master_product.high_similar_ids.ids)],
                "med_similar_ids": [(6, 0, master_product.med_similar_ids.ids)],
                "low_similar_ids": [(6, 0, master_product.low_similar_ids.ids)],
                "related_sample_book_ids": [(6, 0, master_product.related_sample_book_ids.ids)],
                "product_compo_ids": [(6, 0, master_product.product_compo_ids.ids)],
                "product_colour_ids": [(6, 0, master_product.product_colour_ids.ids)],
                "product_pattern_ids": [(6, 0, master_product.product_pattern_ids.ids)],
                "product_yarn_id": master_product.product_yarn_id.id,
                "region_of_origin_id": master_product.region_of_origin_id.id,
                "sub_categ_id": master_product.sub_categ_id.id,
                "attribute_details": master_product.attribute_details,
                "width_cm": master_product.width_cm,
                "low_weight_g": master_product.low_weight_g,
                "high_weight_g": master_product.high_weight_g,
                "solids": master_product.solids,
                "double_face": master_product.double_face,
                "quick_service": master_product.quick_service,
                "stock_availability": master_product.stock_availability,
                "temp_out_until": master_product.temp_out_until,
                "shipping_info": master_product.shipping_info,
                "keep_stock": master_product.keep_stock,
                "master_collection_id": master_product.master_collection_id,
                "ecom_remarks": master_product.ecom_remarks,

                # todo
                # "inventory_availability": master_product.inventory_availability,
                # "available_threshold": master_product.available_threshold,

                "lab_origin": master_product.lab_origin,
                "lab_content": master_product.lab_content,
                "lab_super": master_product.lab_super,
                "lab_width": master_product.lab_width,
                "lab_feature": master_product.lab_feature,
                "lab_colour": master_product.lab_colour,
                "product_feature": master_product.product_feature,
            }
        )

    def button_update_version_products(self):
        """Update all version of the master product"""
        for master_product in self:
            if master_product.is_master:
                for version in master_product.version_product_ids:
                    version.update_version_products(master_product)

    def button_update_from_master_products(self):
        """Update latest data from the master product"""
        for product in self:
            if product.master_product_id:
                product.update_version_products(product.master_product_id)

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override to pass Internal Reference from the Sequence
        Only for master and Development category
        No need to generate it during import
        """
        templates = super().create(vals_list)
        if "import_file" not in self.env.context:
            Sequence = self.env["ir.sequence"]
            for template in templates:
                if template.is_master and template.is_development:
                    template.default_code = Sequence.next_by_code("default.code")
        return templates

    def action_view_on_cutting_moves(self):
        self.ensure_one()
        cutting_location = self.env["stock.location"]._get_cutting_location()
        domain = [
            "|",
            ("location_id", "=", cutting_location.id),
            ("location_dest_id", "=", cutting_location.id),
            ("product_id.product_tmpl_id", "=", self.id),
        ]
        return self._get_move_line_action(domain)

    def action_view_on_stock_quants(self):
        self.ensure_one()
        cutting_location = self.env["stock.location"]._get_cutting_location()
        domain = [
            ("location_id", "!=", cutting_location.id),
            ("location_id.usage", "=", "internal"),
            ("product_id.product_tmpl_id", "=", self.id),
        ]
        return self.env["stock.quant"]._get_quants_action(domain)

    def action_view_free_inv_moves(self):
        self.ensure_one()
        domain = [("product_id.product_tmpl_id", "=", self.id)]
        return self._get_move_line_action(domain)

    def action_view_pending_po_moves(self):
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "name": "Pending PO",
            "res_model": "stock.move",
            "view_mode": "tree",
            "views": [(self.env.ref("stock.view_move_tree").id, "tree")],
            "domain": [
                ("picking_code", "=", "incoming"),
                ("location_id.usage", "=", "supplier"),
                ("state", "in", ["confirmed", "partially_available", "assigned"]),
                ("product_id.product_tmpl_id", "=", self.id),
            ],
            "context": {
                "create": 0,
            },
        }

    def action_view_pending_so_moves(self):
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "name": "Pending SO",
            "res_model": "stock.move",
            "view_mode": "tree",
            "views": [(self.env.ref("stock.view_move_tree").id, "tree")],
            "domain": [
                ("picking_code", "=", "outgoing"),
                ("state", "not in", ["done", "cancel"]),
                ("product_id.product_tmpl_id", "=", self.id),
            ],
            "context": {
                "create": 0,
            },
        }

        location_ptn_customer = self.env.ref("stock.stock_location_customers")
        domain = [
            ("picking_code", "=", "outgoing"),
            ("state", "in", ["waiting", "confirmed", "partially_available", "assigned"]),
            ("location_dest_id", "=", location_ptn_customer.id),
            ("product_id.product_tmpl_id", "=", self.id),
        ]
        return self._get_move_line_action(domain)

    def _get_move_line_action(self, domain):
        action = self.env["ir.actions.actions"]._for_xml_id("stock.stock_move_line_action")
        action["domain"] = domain
        action["context"] = {"create": 0}
        return action

    def action_view_version_products(self):
        action = self.env["ir.actions.actions"]._for_xml_id("sale.product_template_action")
        action["domain"] = [("id", "in", self.version_product_ids.ids)]
        action["context"] = {"create": 0}
        return action

    def button_update_rank_number(self):
        """
            We have Master products and its Version products
            When client search any product with `Version Family`, we will get all version products and master product
            Now, Plan is when client click on `Update Rank Number` action on tree/form view, it should copy `default_code` of current product
            and update it to `rank_number` of all Version Family(master and its version products)
        """
        for product in self:
            if product.rank:
                products = self.search([('version_family', '=', product.default_code)]) - product
                products.write({"rank_number": product.default_code, 'rank': False})
        return True

    @api.model
    def _get_view(self, view_id=None, view_type='form', **options):
        """OVERRIDE
        1) Add `required` when `is_development` is True
        2) Add `readonly` when we are in Version Product
        3) Make all m2* `create=False`
        """
        arch, view = super()._get_view(view_id, view_type, **options)
        if view_type == "form":
            for node in arch.xpath('//field[not(ancestor::field)]'):
                if node.get('name') in REQUIRED_FOR_DEV:
                    node.attrib["required"] = "is_development"  # Most of fields are custom one so no other values in `required`
                if node.get('name') in READONLY_FOR_VERSION:
                    node.attrib["readonly"] = "master_product_id"
                if node.get('name') in M2O_M2M_NO_CREATE:
                    node.attrib["options"] = "{'no_create': true}"
        return arch, view
