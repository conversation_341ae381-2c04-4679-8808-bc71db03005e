from odoo import api, fields, models


class SaleOrder(models.Model):
    _inherit = "sale.order"

    credit_limit = fields.Float(string="Customer's credit limit", related="partner_id.credit_limit")
    remaining_credit = fields.Monetary(string="Customer's remaining limit", related="partner_id.remaining_credit")
    amount_discounted = fields.Float(string="Discounted Amount", compute="_compute_amount_discounted")
    is_limit_exceed = fields.Boolean(string="Is Limit Exceed?", compute="_compute_is_limit_exceed")

    @api.depends("amount_total")
    def _compute_amount_discounted(self):
        for sale in self:
            sale.amount_discounted = -(sale.amount_undiscounted - sale.amount_total)

    @api.depends("amount_total", "partner_id.remaining_credit", "payment_term_id")
    def _compute_is_limit_exceed(self):
        for sale in self:
            is_limit_exceed = False
            if not sale.payment_term_id.is_cod:
                if sale.state == 'draft':
                    is_limit_exceed = (sale.remaining_credit - sale.amount_total) < 0
                else:
                    is_limit_exceed = sale.remaining_credit < 0
            sale.is_limit_exceed = is_limit_exceed
