<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.artextile_lock_date</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//block[@id='analytic']" position="after">
                <block title="Lock Date">
                    <setting help="Start date to allow account entries posting for Advisors">
                        <field name="lock_start_date"/>
                    </setting>
                    <setting help="End date to allow account entries posting for Advisors">
                        <field name="lock_end_date"/>
                    </setting>
                    <setting help="Start date to allow account entries posting for Non-advisors">
                        <field name="lock_start_date_non_adviser"/>
                    </setting>
                    <setting help="End date to allow account entries posting for Non-advisors">
                        <field name="lock_end_date_non_adviser"/>
                    </setting>
                </block>
            </xpath>
        </field>
    </record>
</odoo>
