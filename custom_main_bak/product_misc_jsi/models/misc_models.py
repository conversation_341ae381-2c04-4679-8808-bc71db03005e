from odoo import fields, models


class ProductComposition(models.Model):
    _name = "product.composition"
    _description = "Product Composition"

    name = fields.Char("Composition")
    active = fields.Boolean(default=True)
    sequence = fields.Integer()
    abbr = fields.Char()
    group = fields.Char()


class ProductYarnCount(models.Model):
    _name = "product.yarn.count"
    _description = "Yarn Count"

    active = fields.Boolean(default=True)
    sequence = fields.Integer()
    name = fields.Char("Super Micron")
    yarn_count = fields.Char()
    type = fields.Selection(selection=[("Super level", "Super level"), ("Micron", "Micron"), ("n/a", "n/a")])


class ProductColour(models.Model):
    _name = "product.colour"
    _description = "Product Colour"

    active = fields.Boolean(default=True)
    sequence = fields.Integer()
    name = fields.Char()
    group = fields.Char()


class ProductPattern(models.Model):
    _name = "product.pattern"
    _description = "Product Pattern"

    active = fields.Boolean(default=True)
    name = fields.Char()


class ProductSubcategory(models.Model):
    _name = "product.subcategory"
    _description = "Product Subcategory"

    active = fields.Boolean(default=True)
    name = fields.Char()


class ProductBrand(models.Model):
    _name = "product.brand"
    _description = "Product Brand"

    active = fields.Boolean(default=True)
    name = fields.Char(translate=True)
    brand_code = fields.Char()
    brand_full_name = fields.Char(translate=True)
    sequence = fields.Integer()
    brand_story = fields.Text(translate=True)


class ProductCollection(models.Model):
    _name = "product.collection"
    _description = "Product Collection"

    # With the simple SQL constraints we can not check case insensitive unique
    def init(self):
        self._cr.execute(
            """CREATE UNIQUE INDEX IF NOT EXISTS unique_callection_name ON product_collection(UPPER(name))"""
        )

    active = fields.Boolean(default=True)
    name = fields.Char("Name-Abbr")
    code = fields.Char()
    composition = fields.Char()
    name_full = fields.Char("Collection-full")
    other_spec = fields.Char()
    pattern_colour = fields.Char("Pattern/colour")
    price_range = fields.Char()
    season = fields.Char()
    super_level = fields.Char()
    weight_range = fields.Char()
    sequence = fields.Integer()
    brand = fields.Many2one("product.brand")
    region_of_origin = fields.Many2one("res.country")
    description = fields.Text()
