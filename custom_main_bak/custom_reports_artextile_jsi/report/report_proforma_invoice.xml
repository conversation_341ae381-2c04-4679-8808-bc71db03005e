<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <template id="report_proforma_invoice_document_artextile_jsi">
        <t t-call="web.html_container">
            <t t-call="custom_reports_artextile_jsi.external_layout_artextile_jsi">
                <div class="page font-size14" >
                    <style>th, td {padding: 4px !important;}</style>
                    <t t-set="widget_float_ar" t-value="{'widget': 'float', 'precision': 2}"/>
                    <t t-set="uom_meter_id" t-value="env.ref('uom.product_uom_meter').id" />
                    <t t-set="is_uom_meter" t-value="any(l.product_uom.id == uom_meter_id for l in doc.order_line)" />
                    <table class="product-table-ar">
                        <thead style="display: table-row-group">
                            <tr t-att-class="'text-center ' + ('th-white-ar' if customer_copy else 'th-gray-ar')">
                                <th width="3%" />
                                <th width="12%"><span>Item #<br />貨號</span></th>
                                <th t-if="not customer_copy" width="10%"></th>
                                <th class="text-nowrap"><span>Order #<br />訂單號碼</span></th>
                                <th><span>Description<br />描述</span></th>
                                <th width="10%"><span>Quantity<br />數量</span></th>
                                <th width="10%"><span>Unit Price<br />單價(HK$)</span></th>
                                <th width="12%" t-if="is_uom_meter"><span>Price/Yard<br />每碼單價(HK$)</span></th>
                                <th width="10%"><span>Amount<br />金額(HK$)</span></th>
                            </tr>
                        </thead>
                        <tbody class="sale_tbody">
                            <t t-set="current_subtotal" t-value="0" />
                            <t t-set="line_no" t-value="1" />
                            <t t-foreach="doc.order_line" t-as="line">
                                <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type">
                                        <td class="text-center" ><span t-out="line_no"/></td>
                                        <td class="text-center"><span t-field="line.product_id.default_code"/></td>
                                        <td class="text-center" t-if="not customer_copy">
                                            <t t-if="line.product_id.rank_number">
                                                <span t-field="doc.date_order" t-options="{&quot;format&quot;: &quot;HHmm&quot;}"/><span t-field="line.product_id.rank_number"/>
                                            </t>
                                        </td>
                                        <td class="text-center"><span t-field="line.customer_po"/></td>
                                        <td class="text-center"><span t-field="line.name"/></td>
                                        <td class="text-end">
                                            <t t-if="int(line.product_uom_qty) == line.product_uom_qty">
                                                <span t-out="int(line.product_uom_qty)" />
                                            </t>
                                            <t t-else="">
                                                <span t-field="line.product_uom_qty" t-options="{'widget': 'float', 'precision': 3}"/>
                                            </t>
                                            <span t-field="line.product_uom" />
                                        </td>
                                        <td class="text-end"><span t-field="line.price_unit" /></td>
                                        <td class="text-end" t-if="is_uom_meter">
                                            <span t-field="line.secondary_uom_list_price" />
                                        </td>
                                        <td class="text-end">
                                            <span
                                                t-out="line.product_uom_qty * line.price_unit"
                                                t-options="widget_float_ar"
                                            />
                                        </td>
                                        <t t-set="line_no" t-value="line_no + 1" />
                                    </t>
                                    <t t-if="line.display_type == 'line_section'">
                                        <td class="text-center" name="td_section_line" colspan="99">
                                            <span t-field="line.name" />
                                        </td>
                                        <t t-set="current_section" t-value="line" />
                                        <t t-set="current_subtotal" t-value="0" />
                                    </t>
                                    <t t-if="line.display_type == 'line_note'">
                                        <td name="td_note_line" colspan="99">
                                            <span t-field="line.name" />
                                        </td>
                                    </t>
                                </tr>
                            </t>
                            <tr>
                                <t t-set="col_size" t-value="5 if is_uom_meter else 4" />
                                <t t-set="col_size" t-value="col_size+1 if not customer_copy else col_size" />
                                <td t-att-colspan="col_size" rowspan="3" t-att-style="report_nodisc and 'border: 1px solid white !important;border-right: 1px solid white !important;'">
                                    <span>REMARKS:<br />
                                    1) Please sign to confirm receipt<br />
                                    2) Cheque(s) payable to <span t-field="doc.company_id" /><br />
                                    3) Payment terms: <span t-field="doc.payment_term_id" /><br />
                                    4) TRACKING #: <span t-field="doc.courier_tracking" /></span>
                                </td>
                                <td colspan="2">
                                    <span t-att-style="
                                        report_nodisc and 'margin-left: -5px;border-left: 1px solid black !important;
                                        height: 31px !important; margin-top: -5px;display: inline-table;position: absolute;'">&amp;nbsp;</span>
                                    <t t-out="'Total 總額(HK$)' if report_nodisc else 'Subtotal 小計(HK$)'"/>
                                </td>
                                <td class="text-end">
                                    <t t-set="amount_subtotal" t-value="sum(l.product_uom_qty * l.price_unit for l in doc.order_line)"/>
                                    <span t-out="amount_subtotal" t-options="widget_float_ar" />
                                </td>
                            </tr>
                            <tr t-if="not report_nodisc">
                                <td colspan="2">Discount 折扣(HK$)</td>
                                <td class="text-end">
                                    <span t-out="doc.amount_total - amount_subtotal" t-options="widget_float_ar"/>
                                </td>
                            </tr>
                            <tr t-if="not report_nodisc">
                                <td colspan="2">Total 總額(HK$)</td>
                                <td class="text-end">
                                    <span t-field="doc.amount_total" t-options="widget_float_ar" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </t>
        </t>
    </template>
    <template id="report_proforma_invoice_artextile_jsi">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="custom_reports_artextile_jsi.report_proforma_invoice_document_artextile_jsi" />
            </t>
        </t>
    </template>
    <template id="report_proforma_invoice_no_disc_artextile_jsi">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-set="report_nodisc" t-value="1" />
                <t t-call="custom_reports_artextile_jsi.report_proforma_invoice_document_artextile_jsi" />
                <t t-call="custom_reports_artextile_jsi.report_proforma_invoice_document_artextile_jsi">
                    <t t-set="customer_copy" t-value="1" />
                </t>
            </t>
        </t>
    </template>
    <template id="report_proforma_invoice_customer_copy_artextile_jsi">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="custom_reports_artextile_jsi.report_proforma_invoice_document_artextile_jsi" />
                <t t-call="custom_reports_artextile_jsi.report_proforma_invoice_document_artextile_jsi">
                    <t t-set="customer_copy" t-value="1" />
                </t>
            </t>
        </t>
    </template>
    <!-- report_saleorder -->
    <template id="report_saleorder_artextile_jsi" inherit_id="sale.report_saleorder">
        <t t-call="sale.report_saleorder_raw" position="attributes">
            <attribute name="t-call">custom_reports_artextile_jsi.report_proforma_invoice_artextile_jsi</attribute>
        </t>
    </template>
</odoo>
