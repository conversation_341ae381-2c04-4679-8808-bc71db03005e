<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_order_form_delivery_automation_jsi" model="ir.ui.view">
            <field name="name">sale.order.form.delivery.automation.jsi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form" />
            <field name="arch" type="xml">
                <!-- By default Unlock button visible only if you active lock feature, as we are bypass this so we need to add it explicitly-->
                <xpath expr="//form//header//button[@name='action_draft']" position="after">
                    <button name="action_lock" type="object" string="Lock"
                        help="If the sale is locked, you can not modify it anymore. However, you will still be able to invoice or deliver."
                        invisible="locked or state != 'sale'"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
