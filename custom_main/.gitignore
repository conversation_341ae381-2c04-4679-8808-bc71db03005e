# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
/.venv
/.pytest_cache

# C extensions
*.so

# Distribution / packaging
.Python
env/
bin/
build/
develop-eggs/
dist/
eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
*.eggs

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.cache
nosetests.xml
coverage.xml

# Translations
*.mo

# Pycharm
.idea

# Eclipse
.settings

# Visual Studio cache/options directory
.vs/
.vscode

# OSX Files
.DS_Store

# Django stuff:
*.log

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# Rope
.ropeproject

# Sphinx documentation
docs/_build/

# Backup files
*~
*.swp

# OCA rules
!static/lib/

# pre-commit config
.eslintrc.yml
.flake8
.isort.cfg
.pre-commit-config.yaml
.prettierrc.yml
.pylintrc
.pylintrc-mandatory

.odoo/